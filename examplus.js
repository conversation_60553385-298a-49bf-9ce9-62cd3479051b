// ==UserScript==
// @name         考呀助手
// @namespace    muyang
// @version      2025-06-05
// @description  帮你通过考试
// <AUTHOR>
// @match        https://exam.alibaba-inc.com/*
// @icon         https://img.alicdn.com/imgextra/i2/O1CN013uMnh71JlBr57XTkK_!!6000000001068-73-tps-16-16.ico
// @grant        none
// @require      https://code.jquery.com/jquery-3.7.0.min.js
// ==/UserScript==

$(window).on('load', function() {
    setTimeout(initScript, 2000);
});

// 启动入口
function initScript() {
    'use strict';
    var blingbling = $('div[class^=Nav--kaoya-homepage-header-container-desktop]');
    blingbling.click(function() {
        console.log(getQuestion());
        var question = getQuestion();
        var result = getResult(question);
        //showAnswer(question, result);
    });
}

// 获取题目
function getQuestion() {
    return document.querySelector('div[class^=AnswerContent--kaoya-homepage-body-question-container-inner-desktop]').innerText.replaceAll('\n\n',":");
}

// 请求获取结果
function getResult(question) {
    var result = {};
    $.ajax({
        url: 'http://localhost:5000/answer',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ question: question }),
        async: false,
        success: function(data) {
            if (data.success) {
                result = {
                    answer: data.answer,
                    confidence: data.confidence,
                    explanation: data.explanation
                };
            } else {
                result = {
                    answer: '获取答案失败',
                    confidence: 0,
                    explanation: data.error || '未知错误'
                };
            }
        },
        error: function(xhr, status, error) {
            console.error('API调用失败:', error);
            result = {
                answer: '服务连接失败',
                confidence: 0,
                explanation: '请确保AI服务已启动'
            };
        }
    });
    return result;
}

// 给出答案提示
function showAnswer(question, result) {
    var answer = result.answer;
    var confidence = result.confidence;

    // 创建简洁的答案显示容器
    var answerHtml = '<div style="background: #e8f5e8; border: 2px solid #4caf50; padding: 15px; margin: 10px 0; border-radius: 8px; text-align: center;">' +
                     '<div style="font-size: 24px; font-weight: bold; color: #2e7d32; margin-bottom: 5px;">🤖 AI答案</div>' +
                     '<div style="font-size: 32px; font-weight: bold; color: #1b5e20; margin: 10px 0;">' + answer + '</div>' +
                     '<div style="font-size: 14px; color: #666;">置信度: ' + confidence + '%</div>' +
                     '</div>';

    // 查找合适的位置插入答案
    var questionContainer = $('div[class^=AnswerContent--kaoya-homepage-body-question-container-inner-desktop]');
    if (questionContainer.length > 0) {
        // 移除之前的AI答案
        questionContainer.find('.ai-answer-container').remove();
        // 添加新的AI答案
        questionContainer.append('<div class="ai-answer-container">' + answerHtml + '</div>');
    }
}

// 下一题清理当前的答案提示
function clearAnswer() {
    $('.ai-answer-container').remove();
}
