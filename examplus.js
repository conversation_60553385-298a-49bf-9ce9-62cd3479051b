// ==UserScript==
// @name         考呀助手
// @namespace    muyang
// @version      2025-06-05
// @description  帮你通过考试
// <AUTHOR>
// @match        https://exam.alibaba-inc.com/*
// @icon         https://img.alicdn.com/imgextra/i2/O1CN013uMnh71JlBr57XTkK_!!6000000001068-73-tps-16-16.ico
// @grant        none
// ==/UserScript==

window.addEventListener('load', () => setTimeout(init, 2000));

function init() {
    const trigger = document.querySelector('div[class^=Nav--kaoya-homepage-header-container-desktop]');
    if (!trigger) return setTimeout(init, 2000);

    trigger.addEventListener('click', async () => {
        const questionEl = document.querySelector('div[class^=AnswerContent--kaoya-homepage-body-question-container-inner-desktop]');
        if (!questionEl) return;

        const question = questionEl.innerText.replaceAll('\n\n', ':');
        console.log('题目:', question);

        const result = await getAnswer(question);
        showAnswer(result);
    });
}

async function getAnswer(question) {
    try {
        const response = await fetch('https://aiservice.yida.run/answer', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ question })
        });

        const data = await response.json();
        return data.success ?
            { answer: data.answer, confidence: data.confidence } :
            { answer: '获取答案失败', confidence: 0 };
    } catch (error) {
        console.error('API调用失败:', error);
        return { answer: '服务连接失败', confidence: 0 };
    }
}

function showAnswer(result) {

    // 清除之前的答案
    document.querySelectorAll('.ai-answer').forEach(el => el.remove());

    // 创建答案显示
    const div = document.createElement('div');
    div.className = 'ai-answer';
    div.style.cssText = 'position:fixed;bottom:0px;left:5px;padding:5px;opacity:0.08;color:#000;z-index:999999;';
    div.textContent = `${result.answer}`;

    document.body.appendChild(div);

    // 点击按钮时清除答案
    document.addEventListener('click', e => {
        if (e.target.tagName === 'BUTTON') div.remove();
    }, { once: true });
}
