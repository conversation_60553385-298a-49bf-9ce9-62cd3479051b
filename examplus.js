// ==UserScript==
// @name         考呀助手
// @namespace    muyang
// @version      2025-06-05
// @description  帮你通过考试
// <AUTHOR>
// @match        https://exam.alibaba-inc.com/*
// @icon         https://img.alicdn.com/imgextra/i2/O1CN013uMnh71JlBr57XTkK_!!6000000001068-73-tps-16-16.ico
// @grant        none
// @require      https://code.jquery.com/jquery-3.7.0.min.js
// ==/UserScript==

$(window).on('load', function() {
    setTimeout(initScript, 2000);
});

// 启动入口
function initScript() {
    'use strict';
    var blingbling = $('div[class^=Nav--kaoya-homepage-header-container-desktop]');
    blingbling.click(function() {
        console.log(getQuestion());
        var question = getQuestion();
        var result = getResult(question);
        showAnswer(question, result);
    });
}

// 获取题目
function getQuestion() {
    return document.querySelector('div[class^=AnswerContent--kaoya-homepage-body-question-container-inner-desktop]').innerText.replaceAll('\n\n',":");
}

// 请求获取结果
function getResult(question) {
    var result = [];
    $.ajax({
        url: 'https://api.example.com/answer',
        type: 'POST',
        data: { question: question },
        async: false,
        success: function(data) {
            result = data;
        }
    });
    return result;
}

// 给出答案提示
function showAnswer(question, result) {
    var answer = result.answer;
    var confidence = result.confidence;
    var answerContainer = question.find('div[class^=Question--answer-container]');
    answerContainer.text('AI答案：' + answer + '（置信度：' + confidence + '%）');
}

// 下一题清理当前的答案提示
function clearAnswer() {
    var answerContainer = $('div[class^=Question--answer-container]');
    answerContainer.text('');
}
