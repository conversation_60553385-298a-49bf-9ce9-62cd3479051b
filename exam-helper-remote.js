// 考呀助手 - 远程加载版本
(function() {
    'use strict';
    
    // 防止重复加载
    if (window.examHelper) return console.log('考呀助手已激活');
    window.examHelper = true;
    
    const API_URL = 'http://localhost:5700/answer';
    let retryCount = 0;
    
    function init() {
        const trigger = document.querySelector('div[class^=Nav--kaoya-homepage-header-container-desktop]');
        if (!trigger && retryCount++ < 5) return setTimeout(init, 2000);
        if (!trigger) return console.error('考呀助手: 页面元素未找到');
        
        console.log('🤖 考呀助手已激活！点击导航栏获取答案');
        trigger.addEventListener('click', handleClick);
        showStatus('考呀助手已激活', '#4caf50');
    }
    
    async function handleClick() {
        const questionEl = document.querySelector('div[class^=AnswerContent--kaoya-homepage-body-question-container-inner-desktop]');
        if (!questionEl) return;
        
        const question = questionEl.innerText.replaceAll('\n\n', ':');
        console.log('题目:', question);
        
        showStatus('🤔 思考中...', '#ffc107');
        
        try {
            const response = await fetch(API_URL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ question })
            });
            
            const data = await response.json();
            const result = data.success ? 
                { answer: data.answer, confidence: data.confidence } : 
                { answer: '获取失败', confidence: 0 };
            
            showAnswer(result);
        } catch (error) {
            console.error('API调用失败:', error);
            showAnswer({ answer: '连接失败', confidence: 0 });
        }
    }
    
    function showAnswer(result) {
        console.log('答案:', result.answer, '置信度:', result.confidence);
        clearDisplay();
        
        const div = document.createElement('div');
        div.className = 'exam-helper';
        div.style.cssText = 'position:fixed;top:20px;right:20px;background:#e8f5e8;border:2px solid #4caf50;padding:15px;border-radius:8px;font-weight:bold;color:#1b5e20;z-index:999999;text-align:center;box-shadow:0 4px 8px rgba(0,0,0,0.2);';
        div.innerHTML = `
            <div style="font-size:14px;margin-bottom:5px;">🤖 AI答案</div>
            <div style="font-size:24px;margin:8px 0;">${result.answer}</div>
            <div style="font-size:12px;color:#666;">置信度: ${result.confidence}%</div>
        `;
        
        document.body.appendChild(div);
        
        // 点击按钮清除 + 5秒自动清除
        const clearHandler = e => e.target.tagName === 'BUTTON' && clearDisplay();
        document.addEventListener('click', clearHandler);
        setTimeout(() => {
            clearDisplay();
            document.removeEventListener('click', clearHandler);
        }, 5000);
    }
    
    function showStatus(text, color) {
        clearDisplay();
        const div = document.createElement('div');
        div.className = 'exam-helper';
        div.style.cssText = `position:fixed;top:20px;right:20px;background:${color};color:white;padding:10px;border-radius:8px;font-weight:bold;z-index:999999;`;
        div.textContent = text;
        document.body.appendChild(div);
        
        if (color === '#4caf50') setTimeout(clearDisplay, 3000); // 激活提示3秒后消失
    }
    
    function clearDisplay() {
        document.querySelectorAll('.exam-helper').forEach(el => el.remove());
    }
    
    // 启动
    document.readyState === 'loading' ? 
        document.addEventListener('DOMContentLoaded', init) : 
        setTimeout(init, 1000);
})();
