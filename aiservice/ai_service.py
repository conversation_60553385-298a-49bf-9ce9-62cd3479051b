import openai
import json
import logging
from typing import Dict, Any
from config import Config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIService:
    """AI大模型服务类"""
    
    def __init__(self):
        """初始化AI服务"""
        self.client = openai.OpenAI(
            api_key=Config.OPENAI_API_KEY,
            base_url=Config.OPENAI_BASE_URL
        )
        self.model = Config.OPENAI_MODEL
        self.max_tokens = Config.MAX_TOKENS
        self.temperature = Config.TEMPERATURE
    
    def analyze_question(self, question: str) -> Dict[str, Any]:
        """
        分析题目并返回答案
        
        Args:
            question (str): 题目内容
            
        Returns:
            str: 包含答案和置信度的字典
        """
        try:
            # 构建提示词
            prompt = self._build_prompt(question)
            
            # 调用大模型
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的考试助手，能够准确分析各种考试题目并给出正确答案。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                timeout=Config.API_TIMEOUT
            )
            
            # 解析响应
            result = self._parse_response(response)
            
            logger.info(f"成功处理题目: {question[:50]}...")
            return result
            
        except Exception as e:
            logger.error(f"处理题目时发生错误: {str(e)}")
            return {
                "answer": "抱歉，无法处理该题目",
                "confidence": 0,
                "error": str(e)
            }
    
    def _build_prompt(self, question: str) -> str:
        """
        构建发送给大模型的提示词
        
        Args:
            question (str): 题目内容
            
        Returns:
            str: 格式化的提示词
        """
        prompt = f"""
你是一个Python和人工智能领域的产品技术专家，以下是一个考试题，文本中会告知这是一个单选还是多选题，同时会给出问题和选项，选项数量不定。

题目：{question}



注意：
1. confidence是你对答案正确性的置信度（0-100）
2. 请在answer中明确给出选项字母（如A、B、C、D），如果有多个选项，请用逗号分隔（如A,B,C）
3. 非选择题可以直接给出答案
"""
        return prompt
    
    def _parse_response(self, response) -> Dict[str, Any]:
        """
        解析大模型的响应
        
        Args:
            response: OpenAI API响应对象
            
        Returns:
            Dict[str, Any]: 解析后的结果
        """
        try:
            content = response.choices[0].message.content.strip()
            
            # 尝试解析JSON
            if content.startswith('{') and content.endswith('}'):
                result = json.loads(content)
            else:
                # 如果不是JSON格式，尝试提取关键信息
                result = {
                    "answer": content,
                    "confidence": 70
                }
            
            # 确保必要字段存在
            if "answer" not in result:
                result["answer"] = "无法确定答案"
            if "confidence" not in result:
                result["confidence"] = 50
                
            return result
            
        except json.JSONDecodeError:
            logger.warning("无法解析JSON响应，使用原始内容")
            return {
                "answer": response.choices[0].message.content.strip(),
                "confidence": 60
            }
        except Exception as e:
            logger.error(f"解析响应时发生错误: {str(e)}")
            return {
                "answer": "解析错误",
                "confidence": 0
            }
