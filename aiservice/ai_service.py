import openai
import json
import logging
from typing import Dict, Any
from config import Config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIService:
    """AI大模型服务类"""

    def __init__(self):
        """初始化AI服务"""
        self.client = openai.OpenAI(
            api_key=Config.OPENAI_API_KEY,
            base_url=Config.OPENAI_BASE_URL
        )
        self.model = Config.OPENAI_MODEL
        self.max_tokens = Config.MAX_TOKENS
        self.temperature = Config.TEMPERATURE

    def analyze_question(self, question: str) -> Dict[str, Any]:
        """
        分析题目并返回答案

        Args:
            question (str): 题目内容

        Returns:
            str: 包含答案和置信度的字典
        """
        try:
            # 构建提示词
            prompt = self._build_prompt(question)

            # 调用大模型
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的考试助手，能够准确分析各种考试题目并给出正确答案。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                timeout=Config.API_TIMEOUT
            )

            # 解析响应
            result = self._parse_response(response)

            logger.info(f"成功处理题目: {question[:50]}...")
            return result

        except Exception as e:
            logger.error(f"处理题目时发生错误: {str(e)}")
            return {
                "answer": "抱歉，无法处理该题目",
                "confidence": 0,
                "error": str(e)
            }

    def _build_prompt(self, question: str) -> str:
        """
        构建发送给大模型的提示词

        Args:
            question (str): 题目内容

        Returns:
            str: 格式化的提示词
        """
        prompt = f"""你是一个考试答题专家。请分析以下题目并给出最简洁的答案。

题目：{question}

重要要求：
1. 如果是选择题，只返回选项字母，如：A 或 A,B,C
2. 如果是判断题，只返回：正确 或 错误
3. 如果是填空题，只返回最简洁的答案
4. 不要给出任何解释、分析或其他信息
5. 答案必须简洁明了，直接可用

请直接给出答案："""
        return prompt

    def _parse_response(self, response) -> Dict[str, Any]:
        """
        解析大模型的响应

        Args:
            response: OpenAI API响应对象

        Returns:
            Dict[str, Any]: 解析后的结果
        """
        try:
            content = response.choices[0].message.content.strip()

            # 清理答案，移除多余的文字
            answer = self._clean_answer(content)

            return {
                "answer": answer,
                "explanation": "简洁答案",
                "confidence": 90
            }

        except Exception as e:
            logger.error(f"解析响应时发生错误: {str(e)}")
            return {
                "answer": "解析错误",
                "explanation": str(e),
                "confidence": 0
            }

    def _clean_answer(self, content: str) -> str:
        """
        清理答案，确保返回最简洁的格式

        Args:
            content (str): 原始响应内容

        Returns:
            str: 清理后的简洁答案
        """
        # 移除常见的前缀和后缀
        content = content.replace("答案是：", "").replace("答案：", "")
        content = content.replace("选择：", "").replace("选项：", "")
        content = content.replace("正确答案是：", "").replace("正确答案：", "")

        # 移除句号、感叹号等标点
        content = content.replace("。", "").replace("！", "").replace(".", "")

        # 如果包含多行，只取第一行
        lines = content.split('\n')
        if lines:
            content = lines[0].strip()

        # 如果答案包含解释，尝试提取选项部分
        if '因为' in content or '所以' in content or '解释' in content:
            # 查找选项字母模式
            import re
            # 匹配单个或多个选项字母
            pattern = r'^[A-Z](?:,[A-Z])*'
            match = re.search(pattern, content)
            if match:
                content = match.group()

        return content.strip()
