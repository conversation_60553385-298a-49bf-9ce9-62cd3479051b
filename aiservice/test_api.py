#!/usr/bin/env python3
"""
API测试脚本
"""

import requests
import json
import time

# 服务地址
BASE_URL = "http://localhost:5000"

def test_health():
    """测试健康检查接口"""
    print("测试健康检查接口...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"错误: {e}")
        return False

def test_single_answer():
    """测试单题解答接口"""
    print("\n测试单题解答接口...")

    test_questions = [
        "以下哪个是正确的Python语法？\nA. print 'hello'\nB. print('hello')\nC. echo 'hello'\nD. console.log('hello')",
        "Python是解释型语言吗？\nA. 正确\nB. 错误",
        "以下哪些是Python的数据类型？（多选）\nA. int\nB. str\nC. list\nD. dict",
        "1+1等于多少？",
        "地球是圆的吗？"
    ]

    for question in test_questions:
        print(f"\n题目: {question}")
        try:
            response = requests.post(
                f"{BASE_URL}/answer",
                json={"question": question},
                headers={"Content-Type": "application/json"}
            )
            print(f"状态码: {response.status_code}")
            result = response.json()
            print(f"成功: {result.get('success')}")
            print(f"答案: {result.get('answer')}")
            print(f"解释: {result.get('explanation')}")
            print(f"置信度: {result.get('confidence')}")

            if not result.get('success'):
                print(f"错误: {result.get('error')}")

        except Exception as e:
            print(f"错误: {e}")

        time.sleep(1)  # 避免请求过快

def test_batch_answer():
    """测试批量解答接口"""
    print("\n测试批量解答接口...")

    questions = [
        "1+1等于多少？",
        "地球是圆的吗？",
        "Python是什么编程语言？"
    ]

    try:
        response = requests.post(
            f"{BASE_URL}/batch-answer",
            json={"questions": questions},
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"成功: {result.get('success')}")

        if result.get('success'):
            for i, item in enumerate(result.get('results', [])):
                print(f"\n题目{i+1}: {item.get('question')}")
                print(f"答案: {item.get('answer')}")
                print(f"解释: {item.get('explanation')}")
                print(f"置信度: {item.get('confidence')}")
        else:
            print(f"错误: {result.get('error')}")

    except Exception as e:
        print(f"错误: {e}")

def test_error_cases():
    """测试错误情况"""
    print("\n测试错误情况...")

    # 测试空请求
    print("测试空请求...")
    try:
        response = requests.post(f"{BASE_URL}/answer")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"错误: {e}")

    # 测试空题目
    print("\n测试空题目...")
    try:
        response = requests.post(
            f"{BASE_URL}/answer",
            json={"question": ""},
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"错误: {e}")

    # 测试不存在的接口
    print("\n测试不存在的接口...")
    try:
        response = requests.get(f"{BASE_URL}/nonexistent")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"错误: {e}")

def main():
    """主测试函数"""
    print("=" * 50)
    print("AI题目解答服务 API测试")
    print("=" * 50)

    # 测试健康检查
    if not test_health():
        print("服务未启动或不可用，请先启动服务")
        return

    # 测试单题解答
    test_single_answer()

    # 测试批量解答
    test_batch_answer()

    # 测试错误情况
    test_error_cases()

    print("\n测试完成！")

if __name__ == "__main__":
    main()
