// 考呀助手 - 书签代码
// 将以下代码复制到浏览器地址栏执行，或保存为书签

// 方式1: 直接在地址栏输入（推荐）
javascript:(function(){if(window.examHelper)return console.log('考呀助手已激活');var s=document.createElement('script');s.src='https://你的域名/exam-helper-remote.js?t='+Date.now();s.onload=function(){console.log('考呀助手加载完成')};s.onerror=function(){console.error('考呀助手加载失败')};document.head.appendChild(s);})();

// 方式2: 本地服务器版本（如果你有本地服务器）
javascript:(function(){if(window.examHelper)return console.log('考呀助手已激活');var s=document.createElement('script');s.src='http://localhost:8080/exam-helper-remote.js?t='+Date.now();s.onload=function(){console.log('考呀助手加载完成')};s.onerror=function(){console.error('考呀助手加载失败')};document.head.appendChild(s);})();

// 方式3: GitHub Pages版本（免费托管）
javascript:(function(){if(window.examHelper)return console.log('考呀助手已激活');var s=document.createElement('script');s.src='https://你的用户名.github.io/项目名/exam-helper-remote.js?t='+Date.now();s.onload=function(){console.log('考呀助手加载完成')};s.onerror=function(){console.error('考呀助手加载失败')};document.head.appendChild(s);})();

// 方式4: CDN版本（如果使用CDN）
javascript:(function(){if(window.examHelper)return console.log('考呀助手已激活');var s=document.createElement('script');s.src='https://cdn.jsdelivr.net/gh/你的用户名/项目名@main/exam-helper-remote.js?t='+Date.now();s.onload=function(){console.log('考呀助手加载完成')};s.onerror=function(){console.error('考呀助手加载失败')};document.head.appendChild(s);})();

/*
使用说明：
1. 将 exam-helper-remote.js 文件上传到你的服务器或GitHub Pages
2. 替换上面代码中的URL为你的实际地址
3. 复制对应的javascript:代码到浏览器地址栏执行
4. 或者将代码保存为浏览器书签，点击书签即可加载

优势：
- 无需安装Tampermonkey
- 支持任何浏览器
- 可以随时更新远程脚本
- 一次加载，整个会话期间有效
*/
