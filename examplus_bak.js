// ==UserScript==
// @name         考呀助手
// @namespace    muyang
// @version      2025-06-05
// @description  帮你通过考试
// <AUTHOR>
// @match        https://exam.alibaba-inc.com/*
// @icon         https://img.alicdn.com/imgextra/i2/O1CN013uMnh71JlBr57XTkK_!!6000000001068-73-tps-16-16.ico
// @grant        none
// @require      https://code.jquery.com/jquery-3.7.0.min.js
// ==/UserScript==

$(window).on('load', function() {
    setTimeout(initScript, 5000);
});

// 启动入口
function initScript() {
    'use strict';
    var blingbling = $('div[class^=Nav--kaoya-homepage-header-container-desktop]');
    if (!blingbling.length) {
        setTimeout(initScript, 5000);
        return;
    }
    blingbling.click(function() {
        var question = getQuestion();
        var result = getResult(question);
        showAnswer(result);
    });
}

// 获取题目
function getQuestion() {
    return document.querySelector('div[class^=AnswerContent--kaoya-homepage-body-question-container-inner-desktop]').innerText.replaceAll('\n\n',":");
}

// 请求获取结果
function getResult(question) {
    var result = {};
    $.ajax({
        url: 'https://aiservice.yida.run/answer',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ question: question }),
        async: false,
        success: function(data) {
            if (data.success) {
                result = {
                    answer: data.answer,
                    confidence: data.confidence
                };
            } else {
                result = {
                    answer: '获取答案失败',
                    confidence: 0
                };
            }
        },
        error: function(xhr, status, error) {
            console.error('API调用失败:', error);
            result = {
                answer: '服务连接失败',
                confidence: 0
            };
        }
    });
    return result;
}

// 给出答案提示
function showAnswer(result) {
    var answer = result.answer;
    var confidence = result.confidence;
    console.log(answer, confidence);
    $('.ai-answer-container').remove();

    var chtml = '<div class="ai-answer-container" style="font-size:12px;position:absolute;top:0;left:0;color:black;opacity:0.08;index:999999;">' + answer + '</div>';

    $('div[class^=Footer--kaoya-homepage-footer-action-container-desktop]').prepend($(chtml));
    $('button').click(function() {
        clearAnswer();
    });
}

// 下一题清理当前的答案提示
function clearAnswer() {
    $('.ai-answer-container').remove();
}
