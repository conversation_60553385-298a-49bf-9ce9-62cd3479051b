# 考呀助手 - 独立版本部署说明

## 📁 文件说明

- `exam-helper-remote.js` - 主要脚本文件（需要上传到服务器）
- `bookmarklet.js` - 书签代码示例
- `loader.js` - 最简加载器代码

## 🚀 部署方式

### 方式1: GitHub Pages（推荐，免费）

1. 在GitHub创建新仓库（如：exam-helper）
2. 上传 `exam-helper-remote.js` 文件
3. 在仓库设置中启用GitHub Pages
4. 访问地址：`https://你的用户名.github.io/exam-helper/exam-helper-remote.js`

### 方式2: 自己的服务器

1. 将 `exam-helper-remote.js` 上传到你的服务器
2. 确保文件可以通过HTTP/HTTPS访问
3. 访问地址：`https://你的域名/exam-helper-remote.js`

### 方式3: CDN（如果使用GitHub）

使用jsDelivr CDN：
`https://cdn.jsdelivr.net/gh/你的用户名/仓库名@main/exam-helper-remote.js`

## 📖 使用方法

### 步骤1: 部署脚本文件
选择上述任一方式部署 `exam-helper-remote.js`

### 步骤2: 获取加载代码
根据你的部署方式，使用对应的加载代码：

**GitHub Pages版本：**
```javascript
javascript:(function(){var s=document.createElement('script');s.src='https://你的用户名.github.io/项目名/exam-helper-remote.js?'+Date.now();document.head.appendChild(s);})();
```

**自己服务器版本：**
```javascript
javascript:(function(){var s=document.createElement('script');s.src='https://你的域名/exam-helper-remote.js?'+Date.now();document.head.appendChild(s);})();
```

**CDN版本：**
```javascript
javascript:(function(){var s=document.createElement('script');s.src='https://cdn.jsdelivr.net/gh/你的用户名/项目名@main/exam-helper-remote.js?'+Date.now();document.head.appendChild(s);})();
```

### 步骤3: 使用

1. 打开考试页面
2. 在浏览器地址栏粘贴上述代码并回车
3. 看到"考呀助手已激活"提示
4. 点击页面导航栏获取答案

## 🔧 高级用法

### 保存为书签
1. 复制对应的javascript代码
2. 在浏览器中添加新书签
3. 将代码粘贴到书签的URL字段
4. 保存书签，以后直接点击即可加载

### 自动加载（可选）
如果想要在特定页面自动加载，可以使用Tampermonkey安装以下脚本：

```javascript
// ==UserScript==
// @name         考呀助手自动加载器
// @match        https://exam.alibaba-inc.com/*
// @grant        none
// ==/UserScript==

(function(){
    var s=document.createElement('script');
    s.src='你的脚本地址?'+Date.now();
    document.head.appendChild(s);
})();
```

## ⚙️ 配置修改

如果需要修改API地址，编辑 `exam-helper-remote.js` 文件中的：
```javascript
const API_URL = 'http://localhost:5700/answer';
```

## 🔍 故障排除

1. **脚本加载失败**：检查URL是否正确，文件是否可访问
2. **CORS错误**：确保服务器支持跨域请求
3. **API连接失败**：检查AI服务是否正常运行
4. **页面元素未找到**：确认在正确的考试页面

## 📝 更新脚本

1. 修改 `exam-helper-remote.js` 文件
2. 重新上传到服务器
3. 由于加载代码中包含时间戳，会自动获取最新版本

## 🎯 优势

- ✅ 无需安装浏览器扩展
- ✅ 支持所有现代浏览器
- ✅ 可以随时更新远程脚本
- ✅ 一次加载，整个会话期间有效
- ✅ 代码简洁，易于维护
