// 考呀助手 - 独立版本
// 版本: 2025-06-05
// 作者: muyang

(function() {
    'use strict';
    
    // 防止重复加载
    if (window.examHelper) {
        console.log('考呀助手已加载');
        return;
    }
    
    window.examHelper = true;
    
    // 配置
    const CONFIG = {
        API_URL: 'http://localhost:5700/answer',
        RETRY_DELAY: 2000,
        MAX_RETRIES: 5
    };
    
    let retryCount = 0;
    
    // 初始化函数
    function init() {
        const trigger = document.querySelector('div[class^=Nav--kaoya-homepage-header-container-desktop]');
        if (!trigger) {
            if (retryCount < CONFIG.MAX_RETRIES) {
                retryCount++;
                console.log(`考呀助手: 等待页面加载... (${retryCount}/${CONFIG.MAX_RETRIES})`);
                setTimeout(init, CONFIG.RETRY_DELAY);
                return;
            } else {
                console.error('考呀助手: 页面元素未找到，请确认在正确的考试页面');
                return;
            }
        }
        
        console.log('考呀助手: 初始化成功！点击导航栏获取答案');
        
        // 绑定点击事件
        trigger.addEventListener('click', handleClick);
        
        // 添加视觉提示
        addVisualIndicator();
    }
    
    // 处理点击事件
    async function handleClick() {
        const questionEl = document.querySelector('div[class^=AnswerContent--kaoya-homepage-body-question-container-inner-desktop]');
        if (!questionEl) {
            console.warn('考呀助手: 未找到题目内容');
            return;
        }
        
        const question = questionEl.innerText.replaceAll('\n\n', ':');
        console.log('考呀助手: 题目 ->', question);
        
        // 显示加载状态
        showLoading();
        
        try {
            const result = await getAnswer(question);
            showAnswer(result);
        } catch (error) {
            console.error('考呀助手: 获取答案失败', error);
            showAnswer({ answer: '获取失败', confidence: 0 });
        }
    }
    
    // 获取答案
    async function getAnswer(question) {
        try {
            const response = await fetch(CONFIG.API_URL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ question })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            return data.success ? 
                { answer: data.answer, confidence: data.confidence } : 
                { answer: '获取答案失败', confidence: 0 };
        } catch (error) {
            console.error('考呀助手: API调用失败', error);
            return { answer: '服务连接失败', confidence: 0 };
        }
    }
    
    // 显示加载状态
    function showLoading() {
        removeAnswer();
        
        const div = document.createElement('div');
        div.className = 'exam-helper-loading';
        div.style.cssText = 'position:fixed;top:20px;right:20px;background:#fff3cd;border:2px solid #ffc107;padding:10px;border-radius:8px;font-weight:bold;color:#856404;z-index:999999;';
        div.textContent = '🤔 思考中...';
        
        document.body.appendChild(div);
    }
    
    // 显示答案
    function showAnswer(result) {
        console.log('考呀助手: 答案 ->', result.answer, '置信度:', result.confidence);
        
        removeAnswer();
        
        const div = document.createElement('div');
        div.className = 'exam-helper-answer';
        div.style.cssText = 'position:fixed;top:20px;right:20px;background:#e8f5e8;border:2px solid #4caf50;padding:15px;border-radius:8px;font-weight:bold;color:#1b5e20;z-index:999999;max-width:200px;text-align:center;box-shadow:0 4px 8px rgba(0,0,0,0.2);';
        div.innerHTML = `
            <div style="font-size:14px;margin-bottom:5px;">🤖 AI答案</div>
            <div style="font-size:24px;margin:8px 0;">${result.answer}</div>
            <div style="font-size:12px;color:#666;">置信度: ${result.confidence}%</div>
        `;
        
        document.body.appendChild(div);
        
        // 点击按钮时清除答案
        document.addEventListener('click', function clearOnClick(e) {
            if (e.target.tagName === 'BUTTON') {
                removeAnswer();
                document.removeEventListener('click', clearOnClick);
            }
        });
        
        // 5秒后自动清除
        setTimeout(removeAnswer, 5000);
    }
    
    // 移除答案显示
    function removeAnswer() {
        document.querySelectorAll('.exam-helper-answer, .exam-helper-loading').forEach(el => el.remove());
    }
    
    // 添加视觉提示
    function addVisualIndicator() {
        const indicator = document.createElement('div');
        indicator.style.cssText = 'position:fixed;top:10px;right:10px;background:#4caf50;color:white;padding:5px 10px;border-radius:15px;font-size:12px;z-index:999998;';
        indicator.textContent = '考呀助手已激活';
        
        document.body.appendChild(indicator);
        
        // 3秒后自动消失
        setTimeout(() => indicator.remove(), 3000);
    }
    
    // 检查页面是否为考试页面
    function checkPage() {
        const isExamPage = window.location.hostname.includes('exam.alibaba-inc.com');
        if (!isExamPage) {
            console.warn('考呀助手: 当前不在考试页面，可能无法正常工作');
        }
        return isExamPage;
    }
    
    // 启动
    console.log('考呀助手: 开始加载...');
    checkPage();
    
    // 如果页面已加载完成，直接初始化；否则等待加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        setTimeout(init, 1000);
    }
    
})();
